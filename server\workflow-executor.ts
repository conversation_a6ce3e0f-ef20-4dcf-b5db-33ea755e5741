import { storage } from "./storage";
import type { Workflow, WorkflowRun } from "@shared/schema";

export interface NodeExecutor {
  execute(nodeData: any, input: any, context: ExecutionContext): Promise<any>;
}

export interface ExecutionContext {
  workflowRun: WorkflowRun;
  workflow: Workflow;
  nodeOutputs: Map<string, any>;
  credentials: Map<number, any>;
}

export class WorkflowExecutor {
  private nodeExecutors: Map<string, NodeExecutor>;

  constructor() {
    this.nodeExecutors = new Map();
    this.registerNodeExecutors();
  }

  private registerNodeExecutors() {
    this.nodeExecutors.set('input', new InputNodeExecutor());
    this.nodeExecutors.set('prompt', new PromptNodeExecutor());
    this.nodeExecutors.set('agent', new AgentNodeExecutor());
    this.nodeExecutors.set('api', new ApiNodeExecutor());
  }

  async executeWorkflow(workflowRunId: number): Promise<void> {
    try {
      const workflowRun = await storage.getWorkflowRun(workflowRunId);
      if (!workflowRun) {
        throw new Error(`Workflow run ${workflowRunId} not found`);
      }

      const workflow = await storage.getWorkflow(workflowRun.workflowId);
      if (!workflow) {
        throw new Error(`Workflow ${workflowRun.workflowId} not found`);
      }

      // Update status to running
      await storage.updateWorkflowRun(workflowRunId, { status: 'running' });

      // Create execution context
      const context: ExecutionContext = {
        workflowRun,
        workflow,
        nodeOutputs: new Map(),
        credentials: new Map()
      };

      // Load credentials
      await this.loadCredentials(context);

      // Get execution order
      const executionOrder = this.getExecutionOrder(workflow);

      // Execute nodes in order
      for (const nodeId of executionOrder) {
        await this.executeNode(nodeId, context);
      }

      // Mark workflow as completed
      await storage.updateWorkflowRun(workflowRunId, {
        status: 'completed',
        endTime: new Date()
      });

    } catch (error) {
      console.error(`Workflow execution failed:`, error);
      await storage.updateWorkflowRun(workflowRunId, {
        status: 'failed',
        endTime: new Date()
      });
      throw error;
    }
  }

  private async loadCredentials(context: ExecutionContext): Promise<void> {
    const credentials = await storage.getCredentials();
    for (const credential of credentials) {
      context.credentials.set(credential.id, credential);
    }
  }

  private getExecutionOrder(workflow: Workflow): string[] {
    const nodes = Object.keys(workflow.nodes as Record<string, any>);
    const edges = Object.values(workflow.edges as Record<string, any>);
    const visited = new Set<string>();
    const order: string[] = [];

    // Simple topological sort
    const visit = (nodeId: string) => {
      if (visited.has(nodeId)) return;
      visited.add(nodeId);

      // Visit dependencies first
      const incomingEdges = edges.filter((edge: any) => edge.target === nodeId);
      for (const edge of incomingEdges) {
        visit((edge as any).source);
      }

      order.push(nodeId);
    };

    for (const nodeId of nodes) {
      visit(nodeId);
    }

    return order;
  }

  private async executeNode(nodeId: string, context: ExecutionContext): Promise<void> {
    const node = (context.workflow.nodes as Record<string, any>)[nodeId];
    if (!node) {
      throw new Error(`Node ${nodeId} not found in workflow`);
    }

    // Create node run
    const nodeRun = await storage.createNodeRun({
      workflowRunId: context.workflowRun.id,
      nodeId,
      status: 'running',
      input: this.getNodeInput(nodeId, context),
      output: {}
    });

    try {
      // Get node executor
      const executor = this.nodeExecutors.get(node.type);
      if (!executor) {
        throw new Error(`No executor found for node type: ${node.type}`);
      }

      // Execute node
      const input = this.getNodeInput(nodeId, context);
      const output = await executor.execute(node.data, input, context);

      // Store output
      context.nodeOutputs.set(nodeId, output);

      // Update node run
      await storage.updateNodeRun(nodeRun.id, {
        status: 'completed',
        output,
        endTime: new Date()
      });

    } catch (error) {
      console.error(`Node ${nodeId} execution failed:`, error);
      await storage.updateNodeRun(nodeRun.id, {
        status: 'failed',
        error: error instanceof Error ? error.message : String(error),
        endTime: new Date()
      });
      throw error;
    }
  }

  private getNodeInput(nodeId: string, context: ExecutionContext): any {
    const edges = Object.values(context.workflow.edges as Record<string, any>);
    const incomingEdges = edges.filter((edge: any) => edge.target === nodeId);

    if (incomingEdges.length === 0) {
      // Input node - use workflow run input
      return context.workflowRun.input;
    }

    // Collect outputs from source nodes
    const input: any = {};
    for (const edge of incomingEdges) {
      const sourceOutput = context.nodeOutputs.get((edge as any).source);
      if (sourceOutput !== undefined) {
        Object.assign(input, sourceOutput);
      }
    }

    return input;
  }
}

// Node Executors
class InputNodeExecutor implements NodeExecutor {
  async execute(_nodeData: any, input: any, _context: ExecutionContext): Promise<any> {
    // Input nodes just pass through the input data
    return input;
  }
}

class PromptNodeExecutor implements NodeExecutor {
  async execute(nodeData: any, input: any, context: ExecutionContext): Promise<any> {
    // Get credential
    const credential = context.credentials.get(nodeData.credentialId);
    if (!credential) {
      throw new Error(`Credential ${nodeData.credentialId} not found`);
    }

    // Replace template variables in prompt
    let prompt = nodeData.prompt || '';
    for (const [key, value] of Object.entries(input)) {
      prompt = prompt.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
    }

    // Simulate AI API call (replace with actual implementation)
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    return {
      text: `Generated response for prompt: "${prompt.substring(0, 50)}..."`
    };
  }
}

class AgentNodeExecutor implements NodeExecutor {
  async execute(nodeData: any, input: any, context: ExecutionContext): Promise<any> {
    // Get credential
    const credential = context.credentials.get(nodeData.credentialId);
    if (!credential) {
      throw new Error(`Credential ${nodeData.credentialId} not found`);
    }

    // Simulate agent processing
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));

    if (nodeData.outputFormat === 'json') {
      return {
        result: "Agent processed the input successfully",
        data: input,
        timestamp: new Date().toISOString()
      };
    }

    return {
      text: `Agent response based on: ${JSON.stringify(input).substring(0, 100)}...`
    };
  }
}

class ApiNodeExecutor implements NodeExecutor {
  async execute(_nodeData: any, input: any, _context: ExecutionContext): Promise<any> {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1500));

    return {
      status: 'success',
      response: 'API call completed successfully',
      data: input
    };
  }
}

export const workflowExecutor = new WorkflowExecutor();
